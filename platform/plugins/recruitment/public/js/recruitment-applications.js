$(() => {
    console.log('Recruitment applications JS loaded');

    // Handle export button with current filters
    $(document).on('click', '.export-application-button', function(e) {
        e.preventDefault();
        console.log('Export button clicked');

        // Get current URL with all filters
        const currentUrl = new URL(window.location.href);
        const exportUrl = new URL('/admincp/recruitment-applications/export', window.location.origin);

        // Copy all filter parameters to export URL
        currentUrl.searchParams.forEach((value, key) => {
            exportUrl.searchParams.set(key, value);
        });

        console.log('Export URL with filters:', exportUrl.toString());

        // Navigate to export URL
        window.location.href = exportUrl.toString();
    });
});
